app:
  description: PPT生成工具
  icon: newspaper
  icon_background: '#FBE8FF'
  mode: workflow
  name: PPT生成工具
  use_icon_as_answer_icon: false
kind: app
version: 0.1.4
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: http-request
      id: 1728030320641-source-1728030322836-target
      source: '1728030320641'
      sourceHandle: source
      target: '1728030322836'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: end
      id: 1728030322836-source-1728030383432-target
      source: '1728030322836'
      sourceHandle: source
      target: '1728030383432'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: content
          max_length: 5000
          options: []
          required: true
          type: paragraph
          variable: content
      height: 109
      id: '1728030320641'
      position:
        x: 79
        y: 282
      positionAbsolute:
        x: 79
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: '{{#1728030320641.content#}}'
          type: raw-text
        desc: ''
        headers: ''
        method: post
        params: ''
        selected: true
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: HTTP 请求
        type: http-request
        url: http://host.docker.internal:5004/upload
        variables: []
      height: 134
      id: '1728030322836'
      position:
        x: 381.1375507202415
        y: 282
      positionAbsolute:
        x: 381.1375507202415
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1728030322836'
          - body
          variable: body
        selected: false
        title: 结束
        type: end
      height: 109
      id: '1728030383432'
      position:
        x: 697.047597934702
        y: 282
      positionAbsolute:
        x: 697.047597934702
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    viewport:
      x: -61.83559298659003
      y: -77.54308638669124
      zoom: 0.7220146192362007
